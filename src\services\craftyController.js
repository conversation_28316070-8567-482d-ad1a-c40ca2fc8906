const axios = require('axios');
const { craftyBaseUrl, craftyServerUuid, craftyApiKey } = require('../config/config');
const { trackEvent } = require('../utils/eventTracker');

// Configuração do cliente HTTP com timeout e validação SSL desabilitada para desenvolvimento
const httpClient = axios.create({
    timeout: 30000, // 30 segundos
    httpsAgent: new (require('https').Agent)({
        rejectUnauthorized: false // Para certificados auto-assinados em desenvolvimento
    })
});

/**
 * Classe para gerenciar comunicação com Crafty Controller
 */
class CraftyControllerService {
    constructor() {
        this.baseUrl = craftyBaseUrl;
        this.serverUuid = craftyServerUuid;
        this.apiKey = craftyApiKey;
        this.authToken = null;
        this.tokenExpiry = null;
    }

    /**
     * Autentica com o Crafty Controller usando API Key
     * @returns {Promise<boolean>} True se autenticação foi bem-sucedida
     */
    async authenticate() {
        try {
            console.log('Autenticando com Crafty Controller...');
            
            // Para Crafty Controller, usamos a API Key diretamente no header
            // Não precisamos de autenticação JWT separada se já temos a API Key
            this.authToken = this.apiKey;
            this.tokenExpiry = Date.now() + (24 * 60 * 60 * 1000); // 24 horas
            
            // Testa a autenticação fazendo uma requisição simples
            const response = await httpClient.get(`${this.baseUrl}/api/v2/servers`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 200) {
                console.log('Autenticação com Crafty Controller bem-sucedida');
                trackEvent('crafty_auth_success');
                return true;
            }

            throw new Error(`Falha na autenticação: ${response.status}`);
        } catch (error) {
            console.error('Erro na autenticação com Crafty Controller:', error.message);
            trackEvent('crafty_auth_error', { error: error.message });
            this.authToken = null;
            this.tokenExpiry = null;
            return false;
        }
    }

    /**
     * Verifica se o token ainda é válido
     * @returns {boolean} True se o token é válido
     */
    isTokenValid() {
        return this.authToken && this.tokenExpiry && Date.now() < this.tokenExpiry;
    }

    /**
     * Garante que temos um token válido, reautenticando se necessário
     * @returns {Promise<boolean>} True se temos um token válido
     */
    async ensureAuthenticated() {
        if (this.isTokenValid()) {
            return true;
        }
        return await this.authenticate();
    }

    /**
     * Inicia o servidor Minecraft
     * @returns {Promise<{success: boolean, message: string}>} Resultado da operação
     */
    async startServer() {
        try {
            console.log('Tentando iniciar servidor Minecraft...');

            // Garante que estamos autenticados
            const isAuthenticated = await this.ensureAuthenticated();
            if (!isAuthenticated) {
                return {
                    success: false,
                    message: 'Falha na autenticação com Crafty Controller'
                };
            }

            // Verifica o status atual do servidor antes de tentar iniciar
            const statusCheck = await this.getServerStatus();
            if (statusCheck.success && statusCheck.data?.data?.status === 'running') {
                return {
                    success: true,
                    message: 'Servidor já está rodando!'
                };
            }

            // Faz a requisição para iniciar o servidor
            const response = await httpClient.post(
                `${this.baseUrl}/api/v2/servers/${this.serverUuid}/action/start_server`,
                {}, // Body vazio para o comando start
                {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.status === 200 || response.status === 201) {
                console.log('Comando de iniciar servidor enviado com sucesso');
                trackEvent('server_start_success');
                return {
                    success: true,
                    message: 'Servidor iniciado com sucesso!'
                };
            }

            throw new Error(`Resposta inesperada: ${response.status} - ${response.statusText}`);
        } catch (error) {
            console.error('Erro ao iniciar servidor:', error.message);
            trackEvent('server_start_error', { error: error.message });

            // Mensagens de erro mais específicas
            if (error.code === 'ECONNREFUSED') {
                return {
                    success: false,
                    message: 'Erro ao conectar com o servidor de controle'
                };
            } else if (error.response?.status === 401) {
                return {
                    success: false,
                    message: 'Erro de autenticação'
                };
            } else if (error.response?.status === 404) {
                return {
                    success: false,
                    message: 'Servidor não encontrado'
                };
            } else if (error.code === 'ENOTFOUND') {
                return {
                    success: false,
                    message: 'Servidor de controle não encontrado'
                };
            }

            return {
                success: false,
                message: 'Erro ao iniciar o servidor. Tente novamente.'
            };
        }
    }

    /**
     * Envia um comando diretamente para o console do servidor Minecraft
     * @param {string} commandText - Comando a ser enviado (sem barra inicial)
     * @returns {Promise<{success: boolean, message: string}>} Resultado da operação
     */
    async sendCommand(commandText) {
        try {
            console.log(`Enviando comando para servidor Minecraft: ${commandText}`);

            // Valida entrada
            if (!commandText || typeof commandText !== 'string' || commandText.trim() === '') {
                return {
                    success: false,
                    message: 'Comando não pode estar vazio'
                };
            }

            // Garante que estamos autenticados
            const isAuthenticated = await this.ensureAuthenticated();
            if (!isAuthenticated) {
                return {
                    success: false,
                    message: 'Falha na autenticação com Crafty Controller'
                };
            }

            // Faz a requisição para enviar o comando
            const response = await httpClient.post(
                `${this.baseUrl}/api/v2/servers/${this.serverUuid}/action/send_command`,
                {
                    command: commandText.trim()
                },
                {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.status === 200 || response.status === 201) {
                console.log(`Comando enviado com sucesso: ${commandText}`);
                trackEvent('command_sent_success', { command: commandText });
                return {
                    success: true,
                    message: `✅ Comando enviado ao servidor: ${commandText}`
                };
            }

            throw new Error(`Resposta inesperada: ${response.status} - ${response.statusText}`);
        } catch (error) {
            console.error('Erro ao enviar comando:', error.message);
            trackEvent('command_sent_error', { command: commandText, error: error.message });

            // Mensagens de erro mais específicas
            if (error.code === 'ECONNREFUSED') {
                return {
                    success: false,
                    message: '⚠️ Erro ao conectar com o servidor de controle'
                };
            } else if (error.response?.status === 401) {
                return {
                    success: false,
                    message: '⚠️ Erro de autenticação'
                };
            } else if (error.response?.status === 404) {
                return {
                    success: false,
                    message: '⚠️ Servidor não encontrado'
                };
            } else if (error.code === 'ENOTFOUND') {
                return {
                    success: false,
                    message: '⚠️ Servidor de controle não encontrado'
                };
            }

            return {
                success: false,
                message: '⚠️ Erro ao enviar o comando. Tente novamente.'
            };
        }
    }

    /**
     * Obtém o status do servidor
     * @returns {Promise<{success: boolean, status?: string, message?: string}>} Status do servidor
     */
    async getServerStatus() {
        try {
            const isAuthenticated = await this.ensureAuthenticated();
            if (!isAuthenticated) {
                return {
                    success: false,
                    message: 'Falha na autenticação'
                };
            }

            const response = await httpClient.get(
                `${this.baseUrl}/api/v2/servers/${this.serverUuid}`,
                {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.status === 200 && response.data) {
                return {
                    success: true,
                    status: response.data.status || 'unknown',
                    data: response.data
                };
            }

            throw new Error(`Resposta inesperada: ${response.status}`);
        } catch (error) {
            console.error('Erro ao obter status do servidor:', error.message);
            trackEvent('server_status_error', { error: error.message });
            return {
                success: false,
                message: 'Erro ao verificar status do servidor'
            };
        }
    }
}

// Instância singleton
const craftyController = new CraftyControllerService();

module.exports = craftyController;
