# Controle do Servidor Minecraft via WhatsApp

## Visão Geral

Este bot WhatsApp permite iniciar o servidor Minecraft de forma segura através do comando "iniciar". A integração é feita com o Crafty Controller, que gerencia o servidor Minecraft.

## Funcionalidades Implementadas

### ✅ Comando "iniciar" / "/iniciar"
- **Comandos**: `iniciar` ou `/iniciar` (ambos case-insensitive)
- **Função**: Inicia o servidor Minecraft via Crafty Controller
- **Compatibilidade**: Funciona em chats privados e grupos (quando bot é mencionado)
- **Resposta de Sucesso**: "Servidor iniciado com sucesso!"
- **Resposta de Erro**: "Erro ao iniciar o servidor. Tente novamente."

### ✅ Autenticação Segura
- Utiliza API Key do Crafty Controller armazenada em variável de ambiente
- Token JWT gerenciado automaticamente
- Reautenticação automática quando necessário
- Validação de certificados SSL desabilitada para desenvolvimento

### ✅ Validações de Segurança
- Verifica se o servidor já está rodando antes de tentar iniciar
- Tratamento de erros específicos (conexão, autenticação, servidor não encontrado)
- Logs detalhados para debugging
- Timeout de 30 segundos para requisições HTTP

## Configuração

### Variáveis de Ambiente (.env)
```env
# Configurações do Crafty Controller
CRAFTY_BASE_URL=https://198.1.195.126:8443
CRAFTY_SERVER_UUID=e6e315c1-6554-4ede-bceb-998ceebe9f0b
CRAFTY_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Dependências Adicionadas
- `axios`: Cliente HTTP para comunicação com Crafty Controller API

## Arquitetura

### Arquivos Modificados/Criados

1. **src/services/craftyController.js** (NOVO)
   - Classe `CraftyControllerService` para gerenciar comunicação com API
   - Métodos: `authenticate()`, `startServer()`, `getServerStatus()`
   - Tratamento de erros e logging

2. **src/config/config.js** (MODIFICADO)
   - Adicionadas validações para variáveis do Crafty Controller
   - Exportação das configurações do Crafty Controller

3. **src/utils/utils.js** (MODIFICADO)
   - Adicionado comando "iniciar" no `handleCommand()`
   - Integração com o serviço Crafty Controller
   - Feedback imediato ao usuário

4. **package.json** (MODIFICADO)
   - Adicionada dependência `axios`

## Fluxo de Execução

```
1. Usuário envia "iniciar" via WhatsApp
2. Bot valida o comando (case-insensitive)
3. Envia mensagem "Iniciando servidor... Por favor aguarde."
4. Autentica com Crafty Controller (se necessário)
5. Verifica status atual do servidor
6. Se não estiver rodando, envia comando de start
7. Retorna resultado para o usuário
```

## Tratamento de Erros

### Tipos de Erro Tratados
- **ECONNREFUSED**: "Erro ao conectar com o servidor de controle"
- **401 Unauthorized**: "Erro de autenticação"
- **404 Not Found**: "Servidor não encontrado"
- **ENOTFOUND**: "Servidor de controle não encontrado"
- **Outros**: "Erro ao iniciar o servidor. Tente novamente."

### Logging
- Todos os eventos são registrados via `eventTracker`
- Logs detalhados no console para debugging
- Erros específicos são categorizados

## Segurança

### Medidas Implementadas
- ✅ Credenciais em variáveis de ambiente
- ✅ Validação de entrada (comando exato)
- ✅ Timeout para requisições HTTP
- ✅ Tratamento de erros sem exposição de dados sensíveis
- ✅ Verificação de status antes de iniciar
- ✅ Logs para auditoria

### Considerações de Produção
- Usar certificados SSL válidos (atualmente desabilitado para desenvolvimento)
- Implementar rate limiting para prevenir abuso
- Configurar firewall adequadamente
- Executar Crafty Controller com privilégios limitados

## Testes

### Teste Manual
1. Envie "iniciar" ou "/iniciar" via WhatsApp
2. Verifique se recebe "Iniciando servidor... Por favor aguarde."
3. Aguarde resposta final (sucesso ou erro)
4. Teste ambos os formatos para confirmar compatibilidade

### Teste de Integração
```bash
# Verificar sintaxe
node -c src/services/craftyController.js
node -c src/utils/utils.js
node -c src/config/config.js

# Testar bot (10 segundos)
timeout 10 node index.js
```

## Comandos Disponíveis

| Comando | Descrição |
|---------|-----------|
| `iniciar` ou `/iniciar` | Inicia o servidor Minecraft |
| `/limpar` | Limpa histórico de conversa |
| `/ajuda` | Exibe lista de comandos |

### Formatos de Comando Suportados

**Comando de Iniciar Servidor:**
- `iniciar` - Formato original (mantido para compatibilidade)
- `/iniciar` - Novo formato (consistente com outros comandos)

**Características:**
- ✅ Ambos os formatos são case-insensitive
- ✅ Funcionam em chats privados
- ✅ Funcionam em grupos (quando bot é mencionado ou em resposta)
- ✅ Suportam espaços antes/depois do comando
- ✅ Mesma funcionalidade e segurança para ambos os formatos

## Próximos Passos Sugeridos

1. **Comando "parar"**: Implementar parada do servidor
2. **Status do servidor**: Comando para verificar status atual
3. **Lista de jogadores**: Mostrar jogadores online
4. **Logs do servidor**: Exibir últimas linhas do log
5. **Backup**: Comando para fazer backup do mundo
6. **Rate limiting**: Prevenir spam de comandos
7. **Autorização**: Restringir comandos a usuários específicos

## Troubleshooting

### Problemas Comuns

1. **"Erro de autenticação"**
   - Verificar se CRAFTY_API_KEY está correto
   - Verificar se o token não expirou

2. **"Servidor de controle não encontrado"**
   - Verificar CRAFTY_BASE_URL
   - Verificar conectividade de rede

3. **"Servidor não encontrado"**
   - Verificar CRAFTY_SERVER_UUID
   - Verificar se o servidor existe no Crafty Controller

4. **Bot não responde ao comando**
   - Verificar se o comando é exatamente "iniciar" ou "/iniciar"
   - Em grupos, verificar se o bot foi mencionado ou se é uma resposta
   - Verificar logs do bot para erros
