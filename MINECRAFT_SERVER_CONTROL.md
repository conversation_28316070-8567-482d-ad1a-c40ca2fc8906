# Controle do Servidor Minecraft via WhatsApp

## Visão Geral

Este bot WhatsApp permite iniciar o servidor Minecraft de forma segura através do comando "iniciar". A integração é feita com o Crafty Controller, que gerencia o servidor Minecraft.

## Funcionalidades Implementadas

### ✅ Comando "iniciar" / "/iniciar" (GLOBAL)
- **Comandos**: `iniciar` ou `/iniciar` (ambos case-insensitive)
- **Função**: Inicia o servidor Minecraft via Crafty Controller
- **Comportamento Global**: Funciona em TODOS os contextos sem restrições:
  - ✅ **Chats Privados**: Responde imediatamente
  - ✅ **Grupos**: Responde SEM necessidade de menção (@Taiwo)
  - ✅ **Qualquer contexto**: Execução automática e global
- **Resposta de Sucesso**: "Servidor iniciado com sucesso!"
- **Resposta de Erro**: "Erro ao iniciar o servidor. Tente novamente."

### 🔧 Comando "/comando" (ADMINISTRATIVO)
- **Comando**: `/comando <texto_do_comando>`
- **Função**: Envia comandos diretamente para o console do servidor Minecraft
- **Exemplos**:
  - `/comando difficulty hard`
  - `/comando time set day`
  - `/comando say Hello players`
  - `/comando tp @a 0 100 0`
- **Validações de Segurança**:
  - ⚠️ **Apenas Grupos**: Não funciona em chats privados
  - ⚠️ **Apenas Admins**: Remetente deve ser administrador do grupo
  - ⚠️ **Comando Obrigatório**: Deve ter texto após `/comando `
  - ⚠️ **Lista Autorizada**: Opcional via `AUTHORIZED_USER_IDS`
- **Respostas**:
  - ✅ **Sucesso**: "✅ Comando enviado ao servidor: <comando>"
  - ⛔ **Chat Privado**: "⛔ Esse comando só pode ser usado em grupos."
  - ⛔ **Não Admin**: "⛔ Apenas administradores podem usar esse comando."
  - ⛔ **Comando Vazio**: "⛔ Comando não pode estar vazio. Use: /comando <texto>"
  - ⚠️ **Erro**: "⚠️ Erro ao enviar o comando. Tente novamente."

### ✅ Autenticação Segura
- Utiliza API Key do Crafty Controller armazenada em variável de ambiente
- Token JWT gerenciado automaticamente
- Reautenticação automática quando necessário
- Validação de certificados SSL desabilitada para desenvolvimento

### ✅ Validações de Segurança
- Verifica se o servidor já está rodando antes de tentar iniciar
- Tratamento de erros específicos (conexão, autenticação, servidor não encontrado)
- Logs detalhados para debugging
- Timeout de 30 segundos para requisições HTTP

## Configuração

### Variáveis de Ambiente (.env)
```env
# Configurações do Crafty Controller
CRAFTY_BASE_URL=https://198.1.195.126:8443
CRAFTY_SERVER_UUID=e6e315c1-6554-4ede-bceb-998ceebe9f0b
CRAFTY_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Lista de usuários autorizados para comandos administrativos (opcional)
# Formato: IDs separados por vírgula
AUTHORIZED_USER_IDS=<EMAIL>,<EMAIL>
```

### Dependências Adicionadas
- `axios`: Cliente HTTP para comunicação com Crafty Controller API

## Arquitetura

### Arquivos Modificados/Criados

1. **src/services/craftyController.js** (NOVO)
   - Classe `CraftyControllerService` para gerenciar comunicação com API
   - Métodos: `authenticate()`, `startServer()`, `getServerStatus()`
   - Tratamento de erros e logging

2. **src/config/config.js** (MODIFICADO)
   - Adicionadas validações para variáveis do Crafty Controller
   - Exportação das configurações do Crafty Controller

3. **src/utils/utils.js** (MODIFICADO)
   - Adicionado comando "iniciar" no `handleCommand()`
   - Integração com o serviço Crafty Controller
   - Feedback imediato ao usuário

4. **package.json** (MODIFICADO)
   - Adicionada dependência `axios`

## Fluxo de Execução

```
1. Usuário envia "iniciar" via WhatsApp
2. Bot valida o comando (case-insensitive)
3. Envia mensagem "Iniciando servidor... Por favor aguarde."
4. Autentica com Crafty Controller (se necessário)
5. Verifica status atual do servidor
6. Se não estiver rodando, envia comando de start
7. Retorna resultado para o usuário
```

## Tratamento de Erros

### Tipos de Erro Tratados
- **ECONNREFUSED**: "Erro ao conectar com o servidor de controle"
- **401 Unauthorized**: "Erro de autenticação"
- **404 Not Found**: "Servidor não encontrado"
- **ENOTFOUND**: "Servidor de controle não encontrado"
- **Outros**: "Erro ao iniciar o servidor. Tente novamente."

### Logging
- Todos os eventos são registrados via `eventTracker`
- Logs detalhados no console para debugging
- Erros específicos são categorizados

## Segurança

### Medidas Implementadas
- ✅ Credenciais em variáveis de ambiente
- ✅ Validação de entrada (comando exato)
- ✅ Timeout para requisições HTTP
- ✅ Tratamento de erros sem exposição de dados sensíveis
- ✅ Verificação de status antes de iniciar
- ✅ Logs para auditoria

### Considerações de Produção
- Usar certificados SSL válidos (atualmente desabilitado para desenvolvimento)
- Implementar rate limiting para prevenir abuso
- Configurar firewall adequadamente
- Executar Crafty Controller com privilégios limitados

## Testes

### Teste Manual
1. Envie "iniciar" ou "/iniciar" via WhatsApp
2. Verifique se recebe "Iniciando servidor... Por favor aguarde."
3. Aguarde resposta final (sucesso ou erro)
4. Teste ambos os formatos para confirmar compatibilidade

### Teste de Integração
```bash
# Verificar sintaxe
node -c src/services/craftyController.js
node -c src/utils/utils.js
node -c src/config/config.js

# Testar bot (10 segundos)
timeout 10 node index.js
```

## Comandos Disponíveis

| Comando | Descrição | Restrições |
|---------|-----------|------------|
| `iniciar` ou `/iniciar` | Inicia o servidor Minecraft | Nenhuma (global) |
| `/comando <texto>` | Envia comando para console do servidor | Apenas admins em grupos |
| `/limpar` | Limpa histórico de conversa | Nenhuma |
| `/ajuda` | Exibe lista de comandos | Nenhuma |

### Formatos de Comando Suportados

**Comando de Iniciar Servidor:**
- `iniciar` - Formato original (mantido para compatibilidade)
- `/iniciar` - Novo formato (consistente com outros comandos)

**Características:**
- ✅ Ambos os formatos são case-insensitive
- ✅ Funcionam em chats privados
- ✅ **NOVO**: Funcionam em grupos SEM necessidade de menção
- ✅ Suportam espaços antes/depois do comando
- ✅ Mesma funcionalidade e segurança para ambos os formatos
- ✅ **Comportamento Global**: Execução automática em qualquer contexto

## Comportamento em Grupos vs Chats Privados

### 🔄 **Comandos Globais** (iniciar/iniciar)
- **Chat Privado**: ✅ Funciona normalmente
- **Grupo**: ✅ Funciona SEM menção (@Taiwo)
- **Processamento**: Executado imediatamente, independente do contexto

### 🔧 **Comandos Administrativos** (/comando)
- **Chat Privado**: ❌ Não funciona (apenas grupos)
- **Grupo**: ⚠️ Apenas administradores do grupo
- **Processamento**: Validações de segurança rigorosas
- **Auditoria**: Logs completos de quem executou qual comando

### 🎯 **Outros Comandos** (/ajuda, /limpar)
- **Chat Privado**: ✅ Funciona normalmente
- **Grupo**: ⚠️ Requer menção (@Taiwo) ou resposta ao bot
- **Processamento**: Segue regras tradicionais de bot em grupo

### 💬 **Conversas Normais**
- **Chat Privado**: ✅ Bot responde normalmente
- **Grupo**: ⚠️ Requer menção (@Taiwo) ou resposta ao bot
- **Processamento**: Segue regras tradicionais de bot em grupo

## Exemplos de Uso do Comando /comando

### 🎮 **Comandos de Gameplay**
```
/comando difficulty peaceful
/comando difficulty normal
/comando difficulty hard
/comando gamemode creative
/comando gamemode survival
/comando time set day
/comando time set night
/comando weather clear
/comando weather rain
```

### 👥 **Comandos de Jogadores**
```
/comando tp PlayerName 0 100 0
/comando tp @a 0 100 0
/comando give PlayerName minecraft:diamond 64
/comando effect give @a minecraft:speed 60 1
/comando say Bem-vindos ao servidor!
```

### 🌍 **Comandos de Mundo**
```
/comando setworldspawn
/comando gamerule keepInventory true
/comando gamerule doDaylightCycle false
/comando fill ~-5 ~-1 ~-5 ~5 ~-1 ~5 minecraft:stone
```

### ⚠️ **Comandos Perigosos (Use com Cuidado)**
```
/comando stop
/comando kick PlayerName
/comando ban PlayerName
/comando whitelist add PlayerName
/comando op PlayerName
```

## Próximos Passos Sugeridos

1. **Comando "parar"**: Implementar parada do servidor
2. **Status do servidor**: Comando para verificar status atual
3. **Lista de jogadores**: Mostrar jogadores online
4. **Logs do servidor**: Exibir últimas linhas do log
5. **Backup**: Comando para fazer backup do mundo
6. **Rate limiting**: Prevenir spam de comandos
7. **Autorização**: Restringir comandos a usuários específicos

## Troubleshooting

### Problemas Comuns

1. **"Erro de autenticação"**
   - Verificar se CRAFTY_API_KEY está correto
   - Verificar se o token não expirou

2. **"Servidor de controle não encontrado"**
   - Verificar CRAFTY_BASE_URL
   - Verificar conectividade de rede

3. **"Servidor não encontrado"**
   - Verificar CRAFTY_SERVER_UUID
   - Verificar se o servidor existe no Crafty Controller

4. **Bot não responde ao comando iniciar**
   - Verificar se o comando é exatamente "iniciar" ou "/iniciar"
   - ✅ **NOVO**: Comandos iniciar funcionam em grupos SEM menção
   - Verificar logs do bot para erros

5. **Bot não responde a outros comandos em grupo**
   - Verificar se o bot foi mencionado (@Taiwo)
   - Verificar se é uma resposta a mensagem do bot
   - Comandos /ajuda e /limpar ainda precisam de menção em grupos

6. **Comando /comando não funciona**
   - Verificar se está sendo usado em um grupo (não funciona em chat privado)
   - Verificar se o usuário é administrador do grupo
   - Verificar se o comando não está vazio: `/comando <texto>`
   - Verificar se o usuário está na lista AUTHORIZED_USER_IDS (se configurada)
   - Verificar logs do bot para detalhes do erro

7. **Comando /comando executado mas servidor não responde**
   - Verificar se o servidor Minecraft está rodando
   - Verificar conectividade com Crafty Controller
   - Verificar se o comando Minecraft está correto
   - Alguns comandos podem não ter resposta visível no chat
