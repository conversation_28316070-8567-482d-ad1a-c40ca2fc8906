const { botName, authorizedUserIds } = require('../config/config');
const craftyController = require('../services/craftyController');

// Função para verificar se é um chat de grupo
const isGroupChat = (jid) => jid.endsWith('@g.us');

// Função para verificar se o bot foi mencionado
const isBotMentioned = (text) => {
    if (!text || typeof text !== 'string') return false;
    return new RegExp(`\\b${botName}\\b`, 'i').test(text);
};

// Função para verificar se é um comando global (que funciona em grupos sem menção)
const isGlobalCommand = (text) => {
    if (!text || typeof text !== 'string') return false;
    const lowerText = text.toLowerCase().trim();
    const globalCommands = ['iniciar', '/iniciar'];

    // Verifica se é comando /comando (com qualquer texto após)
    if (lowerText.startsWith('/comando ')) {
        return true;
    }

    return globalCommands.includes(lowerText);
};

// Função para verificar se um usuário é administrador do grupo
const isGroupAdmin = async (socket, chatId, userId) => {
    try {
        // Verifica se é um grupo
        if (!chatId.endsWith('@g.us')) {
            return false;
        }

        // Obtém metadados do grupo
        const groupMetadata = await socket.groupMetadata(chatId);

        // Verifica se o usuário é administrador ou criador do grupo
        const participant = groupMetadata.participants.find(p => p.id === userId);

        if (!participant) {
            return false; // Usuário não está no grupo
        }

        // Verifica se é admin ou super admin (criador)
        return participant.admin === 'admin' || participant.admin === 'superadmin';
    } catch (error) {
        console.error('Erro ao verificar admin do grupo:', error);
        return false; // Em caso de erro, nega permissão por segurança
    }
};

// Função para iniciar servidor (reutilizada para ambos os formatos)
const startMinecraftServer = async (chatId, sendMessage) => {
    console.log(`Comando 'iniciar' recebido de ${chatId}`);
    await sendMessage(chatId, 'Iniciando servidor... Por favor aguarde.');

    try {
        const result = await craftyController.startServer();
        await sendMessage(chatId, result.message);
    } catch (error) {
        console.error('Erro ao processar comando iniciar:', error);
        await sendMessage(chatId, 'Erro ao iniciar o servidor. Tente novamente.');
    }
};

// Função para processar comandos do servidor com validações de segurança
const handleServerCommand = async (fullText, chatId, sendMessage, socket, sender, isGroup) => {
    try {
        console.log(`Comando '/comando' recebido de ${sender} no chat ${chatId}`);

        // Validação 1: Deve ser enviado em um grupo
        if (!isGroup) {
            await sendMessage(chatId, '⛔ Esse comando só pode ser usado em grupos.');
            return;
        }

        // Validação 2: Verificar se o remetente é administrador do grupo
        const isAdmin = await isGroupAdmin(socket, chatId, sender);
        if (!isAdmin) {
            await sendMessage(chatId, '⛔ Apenas administradores podem usar esse comando.');
            return;
        }

        // Validação 3: Extrair o comando do texto
        const commandMatch = fullText.match(/^\/comando\s+(.+)$/i);
        if (!commandMatch || !commandMatch[1] || commandMatch[1].trim() === '') {
            await sendMessage(chatId, '⛔ Comando não pode estar vazio. Use: /comando <texto>');
            return;
        }

        const commandText = commandMatch[1].trim();

        // Validação 4 (Opcional): Verificar se usuário está na lista de autorizados
        if (authorizedUserIds.length > 0 && !authorizedUserIds.includes(sender)) {
            await sendMessage(chatId, '⛔ Você não tem permissão para usar este comando.');
            return;
        }

        // Log de auditoria
        console.log(`Comando executado por ${sender} no grupo ${chatId}: ${commandText}`);

        // Enviar comando para o servidor
        const result = await craftyController.sendCommand(commandText);
        await sendMessage(chatId, result.message);

    } catch (error) {
        console.error('Erro ao processar comando do servidor:', error);
        await sendMessage(chatId, '⚠️ Erro ao processar o comando. Tente novamente.');
    }
};

// Função para tratar comandos
const handleCommand = async (text, chatId, contextId, clearHistory, sendMessage, socket = null, sender = null, isGroup = false) => {
    const lowerText = text.toLowerCase().trim();
    const commandActions = {
        '/limpar': async () => {
            clearHistory(contextId);
            await sendMessage(chatId, 'Histórico limpo. Nova conversa iniciada.');
        },
        '/ajuda': async () => {
            const helpMessage = `
                Comandos:
                /limpar - Limpa histórico
                /ajuda - Exibe esta mensagem de ajuda
                iniciar ou /iniciar - Inicia o servidor Minecraft
                /comando <texto> - Envia comando para o console do servidor (apenas admins em grupos)
            `;
            await sendMessage(chatId, helpMessage);
        },
        'iniciar': async () => {
            await startMinecraftServer(chatId, sendMessage);
        },
        '/iniciar': async () => {
            await startMinecraftServer(chatId, sendMessage);
        }
    };

    // Verifica comandos exatos primeiro
    if (commandActions[lowerText]) {
        await commandActions[lowerText]();
        return true;
    }

    // Verifica comando /comando com parâmetros
    if (lowerText.startsWith('/comando ') || lowerText === '/comando') {
        await handleServerCommand(text, chatId, sendMessage, socket, sender, isGroup);
        return true;
    }

    return false;
};

module.exports = { isGroupChat, isBotMentioned, handleCommand, isGlobalCommand, isGroupAdmin };
