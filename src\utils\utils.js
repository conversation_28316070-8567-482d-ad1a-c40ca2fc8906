const { botName } = require('../config/config');
const craftyController = require('../services/craftyController');

// Função para verificar se é um chat de grupo
const isGroupChat = (jid) => jid.endsWith('@g.us');

// Função para verificar se o bot foi mencionado
const isBotMentioned = (text) => {
    if (!text || typeof text !== 'string') return false;
    return new RegExp(`\\b${botName}\\b`, 'i').test(text);
};

// Função para iniciar servidor (reutilizada para ambos os formatos)
const startMinecraftServer = async (chatId, sendMessage) => {
    console.log(`Comando 'iniciar' recebido de ${chatId}`);
    await sendMessage(chatId, 'Iniciando servidor... Por favor aguarde.');

    try {
        const result = await craftyController.startServer();
        await sendMessage(chatId, result.message);
    } catch (error) {
        console.error('Erro ao processar comando iniciar:', error);
        await sendMessage(chatId, 'Erro ao iniciar o servidor. Tente novamente.');
    }
};

// Função para tratar comandos
const handleCommand = async (text, chatId, contextId, clearHistory, sendMessage) => {
    const lowerText = text.toLowerCase().trim();
    const commandActions = {
        '/limpar': async () => {
            clearHistory(contextId);
            await sendMessage(chatId, 'Histórico limpo. Nova conversa iniciada.');
        },
        '/ajuda': async () => {
            const helpMessage = `
                Comandos:
                /limpar - Limpa histórico
                /ajuda - Exibe esta mensagem de ajuda
                iniciar ou /iniciar - Inicia o servidor Minecraft
            `;
            await sendMessage(chatId, helpMessage);
        },
        'iniciar': async () => {
            await startMinecraftServer(chatId, sendMessage);
        },
        '/iniciar': async () => {
            await startMinecraftServer(chatId, sendMessage);
        }
    };

    // Verifica se o comando existe e executa
    if (commandActions[lowerText]) {
        await commandActions[lowerText]();
        return true;
    }

    return false;
};

module.exports = { isGroupChat, isBotMentioned, handleCommand };
